#include <stdio.h>
#include <unistd.h>
#include <signal.h>
#include "button_control.h"
#include "smart_home_config.h"

/**
 * @file test_buttons.c
 * @brief 按键控制功能测试程序
 * @details 测试GPIO按键控制功能，包括风扇、灯光和主电源控制
 * <AUTHOR> Home System Team
 * @version 1.0.0
 */

static volatile int g_running = 1;

// 信号处理函数
void signal_handler(int sig) {
    printf("\n[INFO] Received signal %d, shutting down...\n", sig);
    g_running = 0;
}

int main() {
    printf("=== Button Control Test Program ===\n");
    printf("Testing GPIO button functionality...\n");
    printf("Press Ctrl+C to exit\n\n");

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 初始化按键控制
    printf("Initializing button control...\n");
    if (button_control_init() != SUCCESS) {
        printf("✗ Failed to initialize button control\n");
        return -1;
    }
    printf("✓ Button control initialized successfully\n\n");

    printf("Button mapping:\n");
    printf("  KEY1 (PF9): Light mode cycling\n");
    printf("  KEY2 (PF7): Main power toggle\n");
    printf("  KEY3 (PF8): Fan toggle\n\n");

    printf("Press buttons to test functionality...\n");
    printf("Current status will be displayed when buttons are pressed.\n\n");

    // 主循环
    while (g_running) {
        // 扫描和处理按键
        button_scan_and_handle();
        
        // 显示当前状态（每5秒显示一次）
        static int counter = 0;
        if (counter % 5 == 0) {
            printf("\r[STATUS] Fan: %s | Light Mode: %d | Main Power: %s | Time: %ds",
                   button_get_fan_state() == FAN_BUTTON_ON ? "ON " : "OFF",
                   button_get_light_mode(),
                   button_get_main_power_state() == MAIN_POWER_BUTTON_ON ? "ON " : "OFF",
                   counter);
            fflush(stdout);
        }
        counter++;
        
        // 短暂休眠
        usleep(200000); // 200ms
    }

    printf("\n\nCleaning up...\n");
    button_control_cleanup();
    printf("✓ Button control test completed\n");

    return 0;
}
