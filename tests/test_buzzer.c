#include <stdio.h>
#include <unistd.h>
#include "actuators.h"
#include "smart_home_config.h"

/**
 * @file test_buzzer.c
 * @brief 蜂鸣器功能测试程序
 * @details 测试蜂鸣器的各种功能，包括不同频率、持续时间和报警功能
 * <AUTHOR> Home System Team
 * @version 1.0.0
 */

int main() {
    printf("=== Buzzer Test Program ===\n");
    printf("Testing buzzer functionality...\n\n");

    // 测试1: 基本频率设置
    printf("Test 1: Setting different frequencies\n");
    
    printf("Setting buzzer to low frequency (%d Hz)...\n", BUZZER_FREQ_LOW);
    if (set_buzzer_frequency(BUZZER_FREQ_LOW) == SUCCESS) {
        printf("✓ Low frequency set successfully\n");
        sleep(2);
    } else {
        printf("✗ Failed to set low frequency\n");
    }
    
    printf("Setting buzzer to medium frequency (%d Hz)...\n", BUZZER_FREQ_MEDIUM);
    if (set_buzzer_frequency(BUZZER_FREQ_MEDIUM) == SUCCESS) {
        printf("✓ Medium frequency set successfully\n");
        sleep(2);
    } else {
        printf("✗ Failed to set medium frequency\n");
    }
    
    printf("Setting buzzer to high frequency (%d Hz)...\n", BUZZER_FREQ_HIGH);
    if (set_buzzer_frequency(BUZZER_FREQ_HIGH) == SUCCESS) {
        printf("✓ High frequency set successfully\n");
        sleep(2);
    } else {
        printf("✗ Failed to set high frequency\n");
    }
    
    // 关闭蜂鸣器
    printf("Turning off buzzer...\n");
    if (buzzer_off() == SUCCESS) {
        printf("✓ Buzzer turned off successfully\n");
    } else {
        printf("✗ Failed to turn off buzzer\n");
    }
    
    sleep(1);
    
    // 测试2: 蜂鸣功能（指定持续时间）
    printf("\nTest 2: Beep functionality\n");
    
    printf("Beeping at 800Hz for 500ms...\n");
    if (buzzer_beep(800, 500) == SUCCESS) {
        printf("✓ Short beep completed\n");
    } else {
        printf("✗ Short beep failed\n");
    }
    
    sleep(1);
    
    printf("Beeping at 1200Hz for 1000ms...\n");
    if (buzzer_beep(1200, 1000) == SUCCESS) {
        printf("✓ Long beep completed\n");
    } else {
        printf("✗ Long beep failed\n");
    }
    
    sleep(1);
    
    // 测试3: 报警功能
    printf("\nTest 3: Alarm functionality\n");
    
    printf("Sounding alarm for 1.5 seconds...\n");
    if (buzzer_alarm(1500) == SUCCESS) {
        printf("✓ Alarm completed\n");
    } else {
        printf("✗ Alarm failed\n");
    }
    
    sleep(1);
    
    // 测试4: 连续蜂鸣模式
    printf("\nTest 4: Continuous beeping pattern\n");
    
    printf("Playing beep pattern (3 short beeps)...\n");
    for (int i = 0; i < 3; i++) {
        printf("Beep %d/3\n", i + 1);
        if (buzzer_beep(BUZZER_FREQ_MEDIUM, 200) != SUCCESS) {
            printf("✗ Beep %d failed\n", i + 1);
        }
        sleep(1); // 间隔1秒
    }
    printf("✓ Beep pattern completed\n");
    
    // 测试5: 错误处理
    printf("\nTest 5: Error handling\n");
    
    printf("Testing invalid duration (0ms)...\n");
    if (buzzer_beep(1000, 0) != SUCCESS) {
        printf("✓ Invalid duration properly rejected\n");
    } else {
        printf("✗ Invalid duration not rejected\n");
    }
    
    printf("Testing negative duration (-100ms)...\n");
    if (buzzer_beep(1000, -100) != SUCCESS) {
        printf("✓ Negative duration properly rejected\n");
    } else {
        printf("✗ Negative duration not rejected\n");
    }
    
    // 最终清理
    printf("\nFinal cleanup...\n");
    buzzer_off();
    printf("✓ Buzzer test completed successfully\n");
    
    printf("\n=== Test Summary ===\n");
    printf("All buzzer tests completed.\n");
    printf("Check the output above for any failed tests.\n");
    printf("If you heard the buzzer sounds, the hardware is working correctly.\n");
    
    return 0;
}
