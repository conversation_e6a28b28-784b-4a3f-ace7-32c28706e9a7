# Button Control Integration Guide

## Overview
The smart home system now includes comprehensive GPIO button control functionality, integrated from the original `button_fan.c`, `button_light.c`, and `button_main.c` files. This provides physical button control for all major system functions.

## Features Added

### 🔘 **Button Mapping**
- **KEY1 (PF9)**: Light mode cycling control
- **KEY2 (PF7)**: Main power toggle control  
- **KEY3 (PF8)**: Fan toggle control

### 🎛️ **Control Functions**

#### **Fan Control (KEY3)**
- Toggle between ON/OFF states
- When ON: Sets fan to full speed (PWM=255)
- When OFF: Stops fan (PWM=0)

#### **Light Control (KEY1)**
- Cycles through 6 light modes:
  1. All lights OFF
  2. Light 1 only
  3. Light 2 only
  4. Light 3 only
  5. Light 4 only
  6. Light 5 only
- Automatically returns to "All OFF" after Light 5

#### **Main Power Control (KEY2)**
- Master switch for all devices
- When ON: Activates all 5 LEDs + Fan
- When OFF: Deactivates all devices

### 🛡️ **Safety Features**
- **Button debouncing**: 200ms delay prevents accidental multiple triggers
- **Conditional compilation**: Works with or without GPIO hardware
- **Resource management**: Proper GPIO cleanup on exit
- **Error handling**: Graceful fallback when GPIO unavailable

## File Structure

### **New Files Added**
```
include/button_control.h     # Button control interface
src/button_control.c         # Unified button control implementation
tests/test_buttons.c         # Button functionality test program
src/button_fan.c            # Original fan button code (moved)
src/button_light.c          # Original light button code (moved)  
src/button_main.c           # Original main power button code (moved)
```

### **Modified Files**
```
CMakeLists.txt              # Added button control to build
src/main.c                  # Integrated button scanning and cleanup
```

## Usage

### **Runtime Integration**
The button control is automatically integrated into the main system:
- Initializes during system startup
- Scans buttons every second in main loop
- Provides immediate response to button presses
- Cleans up resources on shutdown

### **Testing**
```bash
# Test button functionality
./test_buttons

# Run full system with button support
./smart_home_system
```

### **Button Actions**
- **Single press**: Executes the assigned function
- **Multiple presses**: Each press cycles to next state (for light control)
- **Hold**: Same as single press (no special hold function)

## Hardware Requirements

### **For A7 Development Board**
```bash
# Install GPIO library
sudo apt-get install libgpiod-dev

# Enable GPIO support in build
# Uncomment in CMakeLists.txt:
# add_definitions(-DENABLE_GPIO_BUTTONS)
# target_link_libraries(smart_home_system gpiod mosquitto Threads::Threads)
```

### **GPIO Pin Configuration**
- **GPIOF chip** (GPIO bank F)
- **KEY1**: PF9 (Pin 9) - Light control
- **KEY2**: PF7 (Pin 7) - Main power  
- **KEY3**: PF8 (Pin 8) - Fan control
- **Logic**: Active LOW (pressed = 0V, released = 3.3V)

## Software Architecture

### **Conditional Compilation**
```c
#ifdef ENABLE_GPIO_BUTTONS
    // GPIO hardware code
#else
    // Simulation mode
#endif
```

### **State Management**
- Maintains current state for all controls
- Thread-safe button scanning
- Integrates with existing actuator functions

### **Integration Points**
1. **Initialization**: `button_control_init()` in system startup
2. **Main Loop**: `button_scan_and_handle()` every cycle
3. **Cleanup**: `button_control_cleanup()` on shutdown
4. **Status**: Getter functions for current states

## API Reference

### **Core Functions**
```c
int button_control_init(void);                    // Initialize button system
void button_scan_and_handle(void);               // Scan and process buttons
void button_control_cleanup(void);               // Cleanup resources

// Individual button checks
int button_check_fan_key(void);                  // Check fan button
int button_check_light_key(void);                // Check light button  
int button_check_main_power_key(void);           // Check main power button

// State getters
fan_button_state_t button_get_fan_state(void);
light_button_mode_t button_get_light_mode(void);
main_power_button_state_t button_get_main_power_state(void);
```

## Deployment Notes

### **Current Status**
- ✅ Code integrated and compiles successfully
- ✅ Simulation mode works (no GPIO hardware needed)
- ✅ All button logic implemented and tested
- ⚠️ GPIO hardware support requires libgpiod-dev installation
- ⚠️ Physical testing requires A7 development board

### **For Production Deployment**
1. Install GPIO development library
2. Enable GPIO compilation flags
3. Test physical button connections
4. Verify button logic levels and debouncing

The button control system is now fully integrated and ready for deployment on the A7 development board!
