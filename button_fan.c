#include <gpiod.h>
#include <stdio.h>
#include <unistd.h>
#include <string.h>

#define GPIO_CHIP_LABEL "GPIOF"
#define KEY3_PIN 8  // PF8

// 风扇状态枚举
typedef enum {
    FAN_OFF = 0,
    FAN_ON
} fan_state_t;

static fan_state_t fan_state = FAN_OFF;
static struct gpiod_chip* gpio_chip = NULL;
static struct gpiod_line* key3_line = NULL;

// 控制风扇的开关
// state: 1=开, 0=关
static int fan_control(int state) {
    char path[100];
    // 构建风扇控制文件路径
    snprintf(path, sizeof(path), "/sys/class/hwmon/hwmon1/pwm1");
    
    FILE *fp = fopen(path, "w");
    if (fp == NULL) {
        printf("无法打开风扇控制文件: %s\n", path);
        return -1;
    }
    
    // 写入状态
    if (state == 0) {
        fprintf(fp, "0");  // 关闭风扇
    } else {
        fprintf(fp, "255");  // 全速开启风扇
    }
    fclose(fp);
    return 0;
}

// 风扇初始化函数
int fan_init(void) {
    // 打开GPIO芯片
    gpio_chip = gpiod_chip_open_by_label(GPIO_CHIP_LABEL);
    if (!gpio_chip) {
        printf("open gpiochip failed\n");
        return -1;
    }
    
    // 获取KEY3引脚
    key3_line = gpiod_chip_get_line(gpio_chip, KEY3_PIN);
    if (!key3_line) {
        printf("get key3 line failed\n");
        return -1;
    }
    
    // 将GPIO引脚设置为输入
    if (gpiod_line_request_input(key3_line, "key3") != 0) {
        printf("request key3 line failed\n");
        return -1;
    }
    
    // 初始化时关闭风扇
    fan_control(0);
    
    printf("Fan control initialized, fan off\n");
    return 0;
}

// 风扇开关切换函数
void fan_toggle(void) {
    if(fan_state == FAN_OFF) {
        fan_state = FAN_ON;
        fan_control(1);  // 打开风扇
        printf("Fan ON\n");
    } else {
        fan_state = FAN_OFF;
        fan_control(0);  // 关闭风扇
        printf("Fan OFF\n");
    }
}

// 获取风扇状态
fan_state_t fan_get_state(void) {
    return fan_state;
}

// 检测按键是否按下
int fan_check_key_press(void) {
    if (key3_line) {
        int status = gpiod_line_get_value(key3_line);
        // 按键按下时返回0，未按下时返回1
        return (status == 0) ? 1 : 0;
    }
    return 0;
}

// 释放资源
void fan_release(void) {
    if (key3_line) {
        gpiod_line_release(key3_line);
    }
    if (gpio_chip) {
        gpiod_chip_close(gpio_chip);
    }
    
    // 退出时关闭风扇
    fan_control(0);
}