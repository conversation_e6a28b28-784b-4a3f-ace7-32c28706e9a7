#include <gpiod.h>
#include <stdio.h>
#include <unistd.h>
#include <string.h>

#define GPIO_CHIP_LABEL "GPIOF"
#define KEY2_PIN 7  // PF7

// 电源状态枚举
typedef enum {
    MAIN_POWER_OFF = 0,
    MAIN_POWER_ON
} main_power_state_t;

static main_power_state_t main_power_state = MAIN_POWER_OFF;
static struct gpiod_chip* gpio_chip = NULL;
static struct gpiod_line* key2_line = NULL;

// 控制单个LED的亮灭:静态函数
// led_num: LED编号
// state: 1=开, 0=关
static int led_control(int led_num, int state) {
    char path[100];
    // 构建LED控制文件路径
    snprintf(path, sizeof(path), "/sys/class/leds/led%d/brightness", led_num);
    
    FILE *fp = fopen(path, "w");
    if (fp == NULL) {
        return -1;
    }
    
    // 写入状态
    fprintf(fp, "%d", state);
    fclose(fp);
    return 0;
}

// 控制风扇的开关
// state: 1=开, 0=关
static int fan_control(int state) {
    char path[100];
    // 构建风扇控制文件路径
    snprintf(path, sizeof(path), "/sys/class/hwmon/hwmon1/pwm1");
    
    FILE *fp = fopen(path, "w");
    if (fp == NULL) {
    
        return -1;
    }
    
    // 写入状态:风扇控制可能需要特定值，0表示关闭，其他值表示开启
    if (state == 0) {
        fprintf(fp, "0");  // 关闭风扇
    } else {
        fprintf(fp, "255");  // 全速开启风扇
    }
    fclose(fp);
    return 0;
}

// 控制所有设备的开关
static void control_all_devices(int state) {
    // 控制5个LED灯
    for (int i = 1; i <= 5; i++) {
        led_control(i, state);
    }
    
    // 控制风扇
    fan_control(state);
    
    if (state == 0) {
        printf("All devices OFF\n");
    } else {
        printf("All devices ON\n");
    }
}

// 总电源初始化函数
int main_power_init(void) {
    // 打开GPIO芯片
    gpio_chip = gpiod_chip_open_by_label(GPIO_CHIP_LABEL);
    if (!gpio_chip) {
        printf("open gpiochip failed\n");
        return -1;
    }
    
    // 获取KEY2引脚
    key2_line = gpiod_chip_get_line(gpio_chip, KEY2_PIN);
    if (!key2_line) {
        printf("get key2 line failed\n");
        return -1;
    }
    
    // 将GPIO引脚设置为输入
    if (gpiod_line_request_input(key2_line, "key2") != 0) {
        printf("request key2 line failed\n");
        return -1;
    }
    
    // 初始化时关闭所有设备
    control_all_devices(0);
    
    printf("Main power control initialized, all devices off\n");
    return 0;
}

// 总电源开关切换函数
void main_power_toggle(void) {
    if(main_power_state == MAIN_POWER_OFF) {
        main_power_state = MAIN_POWER_ON;
        control_all_devices(1);  // 打开所有设备
        printf("Main power ON\n");
    } else {
        main_power_state = MAIN_POWER_OFF;
        control_all_devices(0);  // 关闭所有设备
        printf("Main power OFF\n");
    }
}

// 获取总电源状态
main_power_state_t main_power_get_state(void) {
    return main_power_state;
}

// 检测按键是否按下
int main_power_check_key_press(void) {
    if (key2_line) {
        int status = gpiod_line_get_value(key2_line);
        // 按键按下时返回0:低电平，未按下时返回1:高电平
        return (status == 0) ? 1 : 0;
    }
    return 0;
}

// 释放资源
void main_power_release(void) {
    if (key2_line) {
        gpiod_line_release(key2_line);
    }
    if (gpio_chip) {
        gpiod_chip_close(gpio_chip);
    }
}