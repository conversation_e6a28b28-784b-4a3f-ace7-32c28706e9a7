[{"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_buttons.dir/tests/test_buttons.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/tests/test_buttons.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/tests/test_buttons.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_buttons.dir/src/file_utils.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/src/file_utils.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/src/file_utils.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_buttons.dir/src/button_control.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/src/button_control.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/src/button_control.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_buzzer.dir/tests/test_buzzer.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/tests/test_buzzer.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/tests/test_buzzer.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_buzzer.dir/src/file_utils.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/src/file_utils.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/src/file_utils.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_buzzer.dir/src/buzzer_control.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/src/buzzer_control.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/src/buzzer_control.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_fan.dir/tests/test_fan.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/tests/test_fan.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/tests/test_fan.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_fan.dir/src/file_utils.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/src/file_utils.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/src/file_utils.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_fan.dir/src/fan_control.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/src/fan_control.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/src/fan_control.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_sensors.dir/tests/test_sensors.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/tests/test_sensors.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/tests/test_sensors.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_sensors.dir/src/file_utils.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/src/file_utils.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/src/file_utils.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_sensors.dir/src/temp_humidity_sensor.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/src/temp_humidity_sensor.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/src/temp_humidity_sensor.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_sensors.dir/src/light_sensor.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/src/light_sensor.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/src/light_sensor.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_sensors.dir/src/current_sensor.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/src/current_sensor.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/src/current_sensor.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/smart_home_system.dir/src/main.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/src/main.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/src/main.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/smart_home_system.dir/src/file_utils.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/src/file_utils.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/src/file_utils.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/smart_home_system.dir/src/temp_humidity_sensor.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/src/temp_humidity_sensor.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/src/temp_humidity_sensor.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/smart_home_system.dir/src/light_sensor.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/src/light_sensor.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/src/light_sensor.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/smart_home_system.dir/src/current_sensor.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/src/current_sensor.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/src/current_sensor.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/smart_home_system.dir/src/fan_control.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/src/fan_control.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/src/fan_control.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/smart_home_system.dir/src/buzzer_control.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/src/buzzer_control.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/src/buzzer_control.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/smart_home_system.dir/src/button_control.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/src/button_control.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/src/button_control.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/smart_home_system.dir/src/data_package.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/src/data_package.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/src/data_package.c"}, {"directory": "/home/<USER>/smart_home_system_final_20250831_1709/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -DENABLE_GPIO_BUTTONS -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/smart_home_system.dir/src/mqtt_client.c.o -c /home/<USER>/smart_home_system_final_20250831_1709/src/mqtt_client.c", "file": "/home/<USER>/smart_home_system_final_20250831_1709/src/mqtt_client.c"}]