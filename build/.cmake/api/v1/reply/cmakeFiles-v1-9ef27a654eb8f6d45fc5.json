{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/3.18.4/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.18.4/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/Compiler/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/Internal/CMakeCheckCompilerFlag.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/Platform/Linux-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/Platform/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/FindPackageMessage.cmake"}], "kind": "cmakeFiles", "paths": {"build": "/home/<USER>/smart_home_system_final_20250831_1709/build", "source": "/home/<USER>/smart_home_system_final_20250831_1709"}, "version": {"major": 1, "minor": 0}}