{"artifacts": [{"path": "test_fan"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 51, "parent": 0}, {"command": 1, "file": 0, "line": 24, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -g -g"}, {"fragment": "-std=gnu99"}], "includes": [{"backtrace": 2, "path": "/home/<USER>/smart_home_system_final_20250831_1709/include"}], "language": "C", "sourceIndexes": [0, 1, 2]}], "id": "test_fan::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wall -Wextra -g -g", "role": "flags"}, {"fragment": "", "role": "flags"}], "language": "C"}, "name": "test_fan", "nameOnDisk": "test_fan", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "tests/test_fan.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/file_utils.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fan_control.c", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}