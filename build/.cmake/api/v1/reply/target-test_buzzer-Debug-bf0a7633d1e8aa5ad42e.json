{"artifacts": [{"path": "test_buzzer"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "add_definitions", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 66, "parent": 0}, {"command": 1, "file": 0, "line": 42, "parent": 0}, {"command": 2, "file": 0, "line": 26, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -g -g"}, {"fragment": "-std=gnu99"}], "defines": [{"backtrace": 2, "define": "ENABLE_GPIO_BUTTONS"}], "includes": [{"backtrace": 3, "path": "/home/<USER>/smart_home_system_final_20250831_1709/include"}], "language": "C", "sourceIndexes": [0, 1, 2]}], "id": "test_buzzer::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wall -Wextra -g -g", "role": "flags"}, {"fragment": "", "role": "flags"}], "language": "C"}, "name": "test_buzzer", "nameOnDisk": "test_buzzer", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "tests/test_buzzer.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/file_utils.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/buzzer_control.c", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}