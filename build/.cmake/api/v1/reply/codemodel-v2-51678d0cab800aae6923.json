{"configurations": [{"directories": [{"build": ".", "minimumCMakeVersion": {"string": "3.10.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "smart_home_system", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "smart_home_system::@6890427a1f51a3e7e1df", "jsonFile": "target-smart_home_system-Debug-80c98ae7f13bb31d2c58.json", "name": "smart_home_system", "projectIndex": 0}, {"directoryIndex": 0, "id": "test_buzzer::@6890427a1f51a3e7e1df", "jsonFile": "target-test_buzzer-Debug-2d3631e4682ad53bc1b1.json", "name": "test_buzzer", "projectIndex": 0}, {"directoryIndex": 0, "id": "test_fan::@6890427a1f51a3e7e1df", "jsonFile": "target-test_fan-Debug-4a5f1d878cf6020d5fdd.json", "name": "test_fan", "projectIndex": 0}, {"directoryIndex": 0, "id": "test_sensors::@6890427a1f51a3e7e1df", "jsonFile": "target-test_sensors-Debug-d40c54e5e2826cf6f66b.json", "name": "test_sensors", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/smart_home_system_final_20250831_1709/build", "source": "/home/<USER>/smart_home_system_final_20250831_1709"}, "version": {"major": 2, "minor": 1}}