{"configurations": [{"directories": [{"build": ".", "minimumCMakeVersion": {"string": "3.10.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "smart_home_system", "targetIndexes": [0, 1, 2, 3, 4]}], "targets": [{"directoryIndex": 0, "id": "smart_home_system::@6890427a1f51a3e7e1df", "jsonFile": "target-smart_home_system-Debug-fb864b1bb23f8b913886.json", "name": "smart_home_system", "projectIndex": 0}, {"directoryIndex": 0, "id": "test_buttons::@6890427a1f51a3e7e1df", "jsonFile": "target-test_buttons-Debug-be087fc3ceca597f19f8.json", "name": "test_buttons", "projectIndex": 0}, {"directoryIndex": 0, "id": "test_buzzer::@6890427a1f51a3e7e1df", "jsonFile": "target-test_buzzer-Debug-bf0a7633d1e8aa5ad42e.json", "name": "test_buzzer", "projectIndex": 0}, {"directoryIndex": 0, "id": "test_fan::@6890427a1f51a3e7e1df", "jsonFile": "target-test_fan-Debug-8bd07d6198b624d3c099.json", "name": "test_fan", "projectIndex": 0}, {"directoryIndex": 0, "id": "test_sensors::@6890427a1f51a3e7e1df", "jsonFile": "target-test_sensors-Debug-b04dba0fb2a09c3a1a31.json", "name": "test_sensors", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/smart_home_system_final_20250831_1709/build", "source": "/home/<USER>/smart_home_system_final_20250831_1709"}, "version": {"major": 2, "minor": 1}}