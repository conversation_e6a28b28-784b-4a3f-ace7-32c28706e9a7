{"artifacts": [{"path": "test_sensors"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 44, "parent": 0}, {"command": 1, "file": 0, "line": 25, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -g -g"}, {"fragment": "-std=gnu99"}], "includes": [{"backtrace": 2, "path": "/home/<USER>/smart_home_system_final_20250831_1709/include"}], "language": "C", "sourceIndexes": [0, 1, 2, 3, 4]}], "id": "test_sensors::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wall -Wextra -g -g", "role": "flags"}, {"fragment": "", "role": "flags"}], "language": "C"}, "name": "test_sensors", "nameOnDisk": "test_sensors", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "tests/test_sensors.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/file_utils.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/temp_humidity_sensor.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/light_sensor.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/current_sensor.c", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}