Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /home/<USER>/smart_home_system_final_20250831_1709/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/ninja cmTC_13d3f && [1/2] Building C object CMakeFiles/cmTC_13d3f.dir/src.c.o
[2/2] Linking C executable cmTC_13d3f
FAILED: cmTC_13d3f 
: && /usr/bin/arm-linux-gnueabihf-gcc -Wall -Wextra -g -DCMAKE_HAVE_LIBC_PTHREAD  CMakeFiles/cmTC_13d3f.dir/src.c.o -o cmTC_13d3f   && :
/usr/lib/gcc-cross/arm-linux-gnueabihf/10/../../../../arm-linux-gnueabihf/bin/ld: CMakeFiles/cmTC_13d3f.dir/src.c.o: in function `main':
/home/<USER>/smart_home_system_final_20250831_1709/build/CMakeFiles/CMakeTmp/src.c:11: undefined reference to `pthread_create'
/usr/lib/gcc-cross/arm-linux-gnueabihf/10/../../../../arm-linux-gnueabihf/bin/ld: /home/<USER>/smart_home_system_final_20250831_1709/build/CMakeFiles/CMakeTmp/src.c:12: undefined reference to `pthread_detach'
/usr/lib/gcc-cross/arm-linux-gnueabihf/10/../../../../arm-linux-gnueabihf/bin/ld: /home/<USER>/smart_home_system_final_20250831_1709/build/CMakeFiles/CMakeTmp/src.c:13: undefined reference to `pthread_cancel'
/usr/lib/gcc-cross/arm-linux-gnueabihf/10/../../../../arm-linux-gnueabihf/bin/ld: /home/<USER>/smart_home_system_final_20250831_1709/build/CMakeFiles/CMakeTmp/src.c:14: undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
ninja: build stopped: subcommand failed.


Source file was:
#include <pthread.h>

void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_cancel(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /home/<USER>/smart_home_system_final_20250831_1709/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/ninja cmTC_c9a26 && [1/2] Building C object CMakeFiles/cmTC_c9a26.dir/CheckFunctionExists.c.o
[2/2] Linking C executable cmTC_c9a26
FAILED: cmTC_c9a26 
: && /usr/bin/arm-linux-gnueabihf-gcc -Wall -Wextra -g -DCHECK_FUNCTION_EXISTS=pthread_create  CMakeFiles/cmTC_c9a26.dir/CheckFunctionExists.c.o -o cmTC_c9a26  -lpthreads && :
/usr/lib/gcc-cross/arm-linux-gnueabihf/10/../../../../arm-linux-gnueabihf/bin/ld: 找不到 -lpthreads
collect2: error: ld returned 1 exit status
ninja: build stopped: subcommand failed.



