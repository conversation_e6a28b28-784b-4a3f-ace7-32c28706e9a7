# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.18

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: smart_home_system
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/smart_home_system_final_20250831_1709/build && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util

# =============================================================================
# Object build statements for EXECUTABLE target test_buzzer


#############################################
# Order-only phony target for test_buzzer

build cmake_object_order_depends_target_test_buzzer: phony || CMakeFiles/test_buzzer.dir

build CMakeFiles/test_buzzer.dir/tests/test_buzzer.c.o: C_COMPILER__test_buzzer_Debug ../tests/test_buzzer.c || cmake_object_order_depends_target_test_buzzer
  DEP_FILE = CMakeFiles/test_buzzer.dir/tests/test_buzzer.c.o.d
  FLAGS = -Wall -Wextra -g -g -std=gnu99
  INCLUDES = -I../include
  OBJECT_DIR = CMakeFiles/test_buzzer.dir
  OBJECT_FILE_DIR = CMakeFiles/test_buzzer.dir/tests

build CMakeFiles/test_buzzer.dir/src/file_utils.c.o: C_COMPILER__test_buzzer_Debug ../src/file_utils.c || cmake_object_order_depends_target_test_buzzer
  DEP_FILE = CMakeFiles/test_buzzer.dir/src/file_utils.c.o.d
  FLAGS = -Wall -Wextra -g -g -std=gnu99
  INCLUDES = -I../include
  OBJECT_DIR = CMakeFiles/test_buzzer.dir
  OBJECT_FILE_DIR = CMakeFiles/test_buzzer.dir/src

build CMakeFiles/test_buzzer.dir/src/buzzer_control.c.o: C_COMPILER__test_buzzer_Debug ../src/buzzer_control.c || cmake_object_order_depends_target_test_buzzer
  DEP_FILE = CMakeFiles/test_buzzer.dir/src/buzzer_control.c.o.d
  FLAGS = -Wall -Wextra -g -g -std=gnu99
  INCLUDES = -I../include
  OBJECT_DIR = CMakeFiles/test_buzzer.dir
  OBJECT_FILE_DIR = CMakeFiles/test_buzzer.dir/src


# =============================================================================
# Link build statements for EXECUTABLE target test_buzzer


#############################################
# Link the executable test_buzzer

build test_buzzer: C_EXECUTABLE_LINKER__test_buzzer_Debug CMakeFiles/test_buzzer.dir/tests/test_buzzer.c.o CMakeFiles/test_buzzer.dir/src/file_utils.c.o CMakeFiles/test_buzzer.dir/src/buzzer_control.c.o
  FLAGS = -Wall -Wextra -g -g
  OBJECT_DIR = CMakeFiles/test_buzzer.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test_buzzer
  TARGET_PDB = test_buzzer.dbg

# =============================================================================
# Object build statements for EXECUTABLE target test_fan


#############################################
# Order-only phony target for test_fan

build cmake_object_order_depends_target_test_fan: phony || CMakeFiles/test_fan.dir

build CMakeFiles/test_fan.dir/tests/test_fan.c.o: C_COMPILER__test_fan_Debug ../tests/test_fan.c || cmake_object_order_depends_target_test_fan
  DEP_FILE = CMakeFiles/test_fan.dir/tests/test_fan.c.o.d
  FLAGS = -Wall -Wextra -g -g -std=gnu99
  INCLUDES = -I../include
  OBJECT_DIR = CMakeFiles/test_fan.dir
  OBJECT_FILE_DIR = CMakeFiles/test_fan.dir/tests

build CMakeFiles/test_fan.dir/src/file_utils.c.o: C_COMPILER__test_fan_Debug ../src/file_utils.c || cmake_object_order_depends_target_test_fan
  DEP_FILE = CMakeFiles/test_fan.dir/src/file_utils.c.o.d
  FLAGS = -Wall -Wextra -g -g -std=gnu99
  INCLUDES = -I../include
  OBJECT_DIR = CMakeFiles/test_fan.dir
  OBJECT_FILE_DIR = CMakeFiles/test_fan.dir/src

build CMakeFiles/test_fan.dir/src/fan_control.c.o: C_COMPILER__test_fan_Debug ../src/fan_control.c || cmake_object_order_depends_target_test_fan
  DEP_FILE = CMakeFiles/test_fan.dir/src/fan_control.c.o.d
  FLAGS = -Wall -Wextra -g -g -std=gnu99
  INCLUDES = -I../include
  OBJECT_DIR = CMakeFiles/test_fan.dir
  OBJECT_FILE_DIR = CMakeFiles/test_fan.dir/src


# =============================================================================
# Link build statements for EXECUTABLE target test_fan


#############################################
# Link the executable test_fan

build test_fan: C_EXECUTABLE_LINKER__test_fan_Debug CMakeFiles/test_fan.dir/tests/test_fan.c.o CMakeFiles/test_fan.dir/src/file_utils.c.o CMakeFiles/test_fan.dir/src/fan_control.c.o
  FLAGS = -Wall -Wextra -g -g
  OBJECT_DIR = CMakeFiles/test_fan.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test_fan
  TARGET_PDB = test_fan.dbg


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/smart_home_system_final_20250831_1709/build && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/smart_home_system_final_20250831_1709 -B/home/<USER>/smart_home_system_final_20250831_1709/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Object build statements for EXECUTABLE target test_sensors


#############################################
# Order-only phony target for test_sensors

build cmake_object_order_depends_target_test_sensors: phony || CMakeFiles/test_sensors.dir

build CMakeFiles/test_sensors.dir/tests/test_sensors.c.o: C_COMPILER__test_sensors_Debug ../tests/test_sensors.c || cmake_object_order_depends_target_test_sensors
  DEP_FILE = CMakeFiles/test_sensors.dir/tests/test_sensors.c.o.d
  FLAGS = -Wall -Wextra -g -g -std=gnu99
  INCLUDES = -I../include
  OBJECT_DIR = CMakeFiles/test_sensors.dir
  OBJECT_FILE_DIR = CMakeFiles/test_sensors.dir/tests

build CMakeFiles/test_sensors.dir/src/file_utils.c.o: C_COMPILER__test_sensors_Debug ../src/file_utils.c || cmake_object_order_depends_target_test_sensors
  DEP_FILE = CMakeFiles/test_sensors.dir/src/file_utils.c.o.d
  FLAGS = -Wall -Wextra -g -g -std=gnu99
  INCLUDES = -I../include
  OBJECT_DIR = CMakeFiles/test_sensors.dir
  OBJECT_FILE_DIR = CMakeFiles/test_sensors.dir/src

build CMakeFiles/test_sensors.dir/src/temp_humidity_sensor.c.o: C_COMPILER__test_sensors_Debug ../src/temp_humidity_sensor.c || cmake_object_order_depends_target_test_sensors
  DEP_FILE = CMakeFiles/test_sensors.dir/src/temp_humidity_sensor.c.o.d
  FLAGS = -Wall -Wextra -g -g -std=gnu99
  INCLUDES = -I../include
  OBJECT_DIR = CMakeFiles/test_sensors.dir
  OBJECT_FILE_DIR = CMakeFiles/test_sensors.dir/src

build CMakeFiles/test_sensors.dir/src/light_sensor.c.o: C_COMPILER__test_sensors_Debug ../src/light_sensor.c || cmake_object_order_depends_target_test_sensors
  DEP_FILE = CMakeFiles/test_sensors.dir/src/light_sensor.c.o.d
  FLAGS = -Wall -Wextra -g -g -std=gnu99
  INCLUDES = -I../include
  OBJECT_DIR = CMakeFiles/test_sensors.dir
  OBJECT_FILE_DIR = CMakeFiles/test_sensors.dir/src

build CMakeFiles/test_sensors.dir/src/current_sensor.c.o: C_COMPILER__test_sensors_Debug ../src/current_sensor.c || cmake_object_order_depends_target_test_sensors
  DEP_FILE = CMakeFiles/test_sensors.dir/src/current_sensor.c.o.d
  FLAGS = -Wall -Wextra -g -g -std=gnu99
  INCLUDES = -I../include
  OBJECT_DIR = CMakeFiles/test_sensors.dir
  OBJECT_FILE_DIR = CMakeFiles/test_sensors.dir/src


# =============================================================================
# Link build statements for EXECUTABLE target test_sensors


#############################################
# Link the executable test_sensors

build test_sensors: C_EXECUTABLE_LINKER__test_sensors_Debug CMakeFiles/test_sensors.dir/tests/test_sensors.c.o CMakeFiles/test_sensors.dir/src/file_utils.c.o CMakeFiles/test_sensors.dir/src/temp_humidity_sensor.c.o CMakeFiles/test_sensors.dir/src/light_sensor.c.o CMakeFiles/test_sensors.dir/src/current_sensor.c.o
  FLAGS = -Wall -Wextra -g -g
  OBJECT_DIR = CMakeFiles/test_sensors.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test_sensors
  TARGET_PDB = test_sensors.dbg

# =============================================================================
# Object build statements for EXECUTABLE target smart_home_system


#############################################
# Order-only phony target for smart_home_system

build cmake_object_order_depends_target_smart_home_system: phony || CMakeFiles/smart_home_system.dir

build CMakeFiles/smart_home_system.dir/src/main.c.o: C_COMPILER__smart_home_system_Debug ../src/main.c || cmake_object_order_depends_target_smart_home_system
  DEP_FILE = CMakeFiles/smart_home_system.dir/src/main.c.o.d
  FLAGS = -Wall -Wextra -g -g -std=gnu99
  INCLUDES = -I../include
  OBJECT_DIR = CMakeFiles/smart_home_system.dir
  OBJECT_FILE_DIR = CMakeFiles/smart_home_system.dir/src

build CMakeFiles/smart_home_system.dir/src/file_utils.c.o: C_COMPILER__smart_home_system_Debug ../src/file_utils.c || cmake_object_order_depends_target_smart_home_system
  DEP_FILE = CMakeFiles/smart_home_system.dir/src/file_utils.c.o.d
  FLAGS = -Wall -Wextra -g -g -std=gnu99
  INCLUDES = -I../include
  OBJECT_DIR = CMakeFiles/smart_home_system.dir
  OBJECT_FILE_DIR = CMakeFiles/smart_home_system.dir/src

build CMakeFiles/smart_home_system.dir/src/temp_humidity_sensor.c.o: C_COMPILER__smart_home_system_Debug ../src/temp_humidity_sensor.c || cmake_object_order_depends_target_smart_home_system
  DEP_FILE = CMakeFiles/smart_home_system.dir/src/temp_humidity_sensor.c.o.d
  FLAGS = -Wall -Wextra -g -g -std=gnu99
  INCLUDES = -I../include
  OBJECT_DIR = CMakeFiles/smart_home_system.dir
  OBJECT_FILE_DIR = CMakeFiles/smart_home_system.dir/src

build CMakeFiles/smart_home_system.dir/src/light_sensor.c.o: C_COMPILER__smart_home_system_Debug ../src/light_sensor.c || cmake_object_order_depends_target_smart_home_system
  DEP_FILE = CMakeFiles/smart_home_system.dir/src/light_sensor.c.o.d
  FLAGS = -Wall -Wextra -g -g -std=gnu99
  INCLUDES = -I../include
  OBJECT_DIR = CMakeFiles/smart_home_system.dir
  OBJECT_FILE_DIR = CMakeFiles/smart_home_system.dir/src

build CMakeFiles/smart_home_system.dir/src/current_sensor.c.o: C_COMPILER__smart_home_system_Debug ../src/current_sensor.c || cmake_object_order_depends_target_smart_home_system
  DEP_FILE = CMakeFiles/smart_home_system.dir/src/current_sensor.c.o.d
  FLAGS = -Wall -Wextra -g -g -std=gnu99
  INCLUDES = -I../include
  OBJECT_DIR = CMakeFiles/smart_home_system.dir
  OBJECT_FILE_DIR = CMakeFiles/smart_home_system.dir/src

build CMakeFiles/smart_home_system.dir/src/fan_control.c.o: C_COMPILER__smart_home_system_Debug ../src/fan_control.c || cmake_object_order_depends_target_smart_home_system
  DEP_FILE = CMakeFiles/smart_home_system.dir/src/fan_control.c.o.d
  FLAGS = -Wall -Wextra -g -g -std=gnu99
  INCLUDES = -I../include
  OBJECT_DIR = CMakeFiles/smart_home_system.dir
  OBJECT_FILE_DIR = CMakeFiles/smart_home_system.dir/src

build CMakeFiles/smart_home_system.dir/src/buzzer_control.c.o: C_COMPILER__smart_home_system_Debug ../src/buzzer_control.c || cmake_object_order_depends_target_smart_home_system
  DEP_FILE = CMakeFiles/smart_home_system.dir/src/buzzer_control.c.o.d
  FLAGS = -Wall -Wextra -g -g -std=gnu99
  INCLUDES = -I../include
  OBJECT_DIR = CMakeFiles/smart_home_system.dir
  OBJECT_FILE_DIR = CMakeFiles/smart_home_system.dir/src

build CMakeFiles/smart_home_system.dir/src/data_package.c.o: C_COMPILER__smart_home_system_Debug ../src/data_package.c || cmake_object_order_depends_target_smart_home_system
  DEP_FILE = CMakeFiles/smart_home_system.dir/src/data_package.c.o.d
  FLAGS = -Wall -Wextra -g -g -std=gnu99
  INCLUDES = -I../include
  OBJECT_DIR = CMakeFiles/smart_home_system.dir
  OBJECT_FILE_DIR = CMakeFiles/smart_home_system.dir/src

build CMakeFiles/smart_home_system.dir/src/mqtt_client.c.o: C_COMPILER__smart_home_system_Debug ../src/mqtt_client.c || cmake_object_order_depends_target_smart_home_system
  DEP_FILE = CMakeFiles/smart_home_system.dir/src/mqtt_client.c.o.d
  FLAGS = -Wall -Wextra -g -g -std=gnu99
  INCLUDES = -I../include
  OBJECT_DIR = CMakeFiles/smart_home_system.dir
  OBJECT_FILE_DIR = CMakeFiles/smart_home_system.dir/src


# =============================================================================
# Link build statements for EXECUTABLE target smart_home_system


#############################################
# Link the executable smart_home_system

build smart_home_system: C_EXECUTABLE_LINKER__smart_home_system_Debug CMakeFiles/smart_home_system.dir/src/main.c.o CMakeFiles/smart_home_system.dir/src/file_utils.c.o CMakeFiles/smart_home_system.dir/src/temp_humidity_sensor.c.o CMakeFiles/smart_home_system.dir/src/light_sensor.c.o CMakeFiles/smart_home_system.dir/src/current_sensor.c.o CMakeFiles/smart_home_system.dir/src/fan_control.c.o CMakeFiles/smart_home_system.dir/src/buzzer_control.c.o CMakeFiles/smart_home_system.dir/src/data_package.c.o CMakeFiles/smart_home_system.dir/src/mqtt_client.c.o
  FLAGS = -Wall -Wextra -g -g
  LINK_LIBRARIES = -lmosquitto  -lmosquitto  -lpthread
  OBJECT_DIR = CMakeFiles/smart_home_system.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = smart_home_system
  TARGET_PDB = smart_home_system.dbg

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/smart_home_system_final_20250831_1709/build

build all: phony test_buzzer test_fan test_sensors smart_home_system

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | ../CMakeLists.txt /usr/share/cmake-3.18/Modules/CMakeCInformation.cmake /usr/share/cmake-3.18/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /usr/share/cmake-3.18/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.18/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.18/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.18/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.18/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.18/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.18/Modules/CheckCSourceCompiles.cmake /usr/share/cmake-3.18/Modules/CheckIncludeFile.cmake /usr/share/cmake-3.18/Modules/CheckLibraryExists.cmake /usr/share/cmake-3.18/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.18/Modules/Compiler/GNU-C.cmake /usr/share/cmake-3.18/Modules/Compiler/GNU.cmake /usr/share/cmake-3.18/Modules/FindPackageHandleStandardArgs.cmake /usr/share/cmake-3.18/Modules/FindPackageMessage.cmake /usr/share/cmake-3.18/Modules/FindThreads.cmake /usr/share/cmake-3.18/Modules/Internal/CMakeCheckCompilerFlag.cmake /usr/share/cmake-3.18/Modules/Platform/Linux-GNU-C.cmake /usr/share/cmake-3.18/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.18/Modules/Platform/Linux.cmake /usr/share/cmake-3.18/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.18.4/CMakeCCompiler.cmake CMakeFiles/3.18.4/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build ../CMakeLists.txt /usr/share/cmake-3.18/Modules/CMakeCInformation.cmake /usr/share/cmake-3.18/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /usr/share/cmake-3.18/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.18/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.18/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.18/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.18/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.18/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.18/Modules/CheckCSourceCompiles.cmake /usr/share/cmake-3.18/Modules/CheckIncludeFile.cmake /usr/share/cmake-3.18/Modules/CheckLibraryExists.cmake /usr/share/cmake-3.18/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.18/Modules/Compiler/GNU-C.cmake /usr/share/cmake-3.18/Modules/Compiler/GNU.cmake /usr/share/cmake-3.18/Modules/FindPackageHandleStandardArgs.cmake /usr/share/cmake-3.18/Modules/FindPackageMessage.cmake /usr/share/cmake-3.18/Modules/FindThreads.cmake /usr/share/cmake-3.18/Modules/Internal/CMakeCheckCompilerFlag.cmake /usr/share/cmake-3.18/Modules/Platform/Linux-GNU-C.cmake /usr/share/cmake-3.18/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.18/Modules/Platform/Linux.cmake /usr/share/cmake-3.18/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.18.4/CMakeCCompiler.cmake CMakeFiles/3.18.4/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
