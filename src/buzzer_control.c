#include "actuators.h"
#include "file_utils.h"
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <linux/input.h>

/**
 * @file buzzer_control.c
 * @brief 蜂鸣器控制模块实现
 * @details 通过Linux input子系统控制蜂鸣器发声
 *          使用input_event结构体向设备文件写入音调控制事件
 * <AUTHOR> Home System Team
 * @version 1.0.0
 */

int set_buzzer_frequency(unsigned int frequency) {
    FILE *fp = fopen(BUZZER_DEVICE_PATH, "w");
    if (fp == NULL) {
        print_error(__func__, "Failed to open buzzer device", ERROR_FILE_OPEN);
        return ERROR_FILE_OPEN;
    }
    
    // 定义输入事件结构体
    struct input_event event;
    event.type = EV_SND;        // 设置事件类型为声音事件
    event.code = SND_TONE;      // 设置事件代码为音调控制
    event.value = frequency;    // 设置音调值，0表示关闭，其他值表示频率
    
    // 将事件结构体写入设备文件
    size_t written = fwrite(&event, sizeof(event), 1, fp);
    fclose(fp);
    
    if (written != 1) {
        print_error(__func__, "Failed to write to buzzer device", ERROR_FILE_WRITE);
        return ERROR_FILE_WRITE;
    }
    
    if (frequency == 0) {
        printf("[INFO] Buzzer turned off\n");
    } else {
        printf("[INFO] Buzzer set to %u Hz\n", frequency);
    }
    
    return SUCCESS;
}

int buzzer_beep(unsigned int frequency, int duration_ms) {
    if (duration_ms <= 0) {
        print_error(__func__, "Invalid duration parameter", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }
    
    // 开启蜂鸣器
    int ret = set_buzzer_frequency(frequency);
    if (ret != SUCCESS) {
        return ret;
    }
    
    // 等待指定时间
    usleep(duration_ms * 1000);  // usleep使用微秒，需要转换
    
    // 关闭蜂鸣器
    return set_buzzer_frequency(BUZZER_OFF);
}

int buzzer_alarm(int duration_ms) {
    return buzzer_beep(BUZZER_FREQ_ALARM, duration_ms);
}

int buzzer_off(void) {
    return set_buzzer_frequency(BUZZER_OFF);
}
