#include "mqtt_client.h"
#include "file_utils.h"
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <mosquitto.h>  // 注释掉，在A7开发板上取消注释

// 全局MQTT状态
static mqtt_status_t g_mqtt_status = MQTT_DISCONNECTED;
static mqtt_message_callback_t g_message_callback = NULL;
static struct mosquitto *g_mosq = NULL;  // 注释掉，在A7开发板上取消注释
static pthread_t g_loop_thread;
static int g_loop_running = 0;


// libmosquitto回调函数 - 在A7开发板上取消注释
static void on_connect(struct mosquitto *mosq, void *userdata, int result) {
    (void)mosq;
    (void)userdata;

    if (result == 0) {
        g_mqtt_status = MQTT_CONNECTED;
        printf("[INFO] MQTT connected successfully\n");
    } else {
        g_mqtt_status = MQTT_ERROR;
        print_error(__func__, "MQTT connection failed", ERROR_MQTT_CONNECT);
    }
}

static void on_disconnect(struct mosquitto *mosq, void *userdata, int result) {
    (void)mosq;
    (void)userdata;
    (void)result;

    g_mqtt_status = MQTT_DISCONNECTED;
    printf("[INFO] MQTT disconnected\n");
}

static void on_message(struct mosquitto *mosq, void *userdata, const struct mosquitto_message *message) {
    (void)mosq;
    (void)userdata;

    if (g_message_callback && message->payload) {
        mqtt_message_t msg;
        strncpy(msg.topic, message->topic, sizeof(msg.topic) - 1);
        msg.topic[sizeof(msg.topic) - 1] = '\0';

        strncpy(msg.payload, (char*)message->payload, sizeof(msg.payload) - 1);
        msg.payload[sizeof(msg.payload) - 1] = '\0';
        msg.payload_len = message->payloadlen;

        printf("[MQTT RECEIVE] Topic: %s\n", msg.topic);
        printf("[MQTT RECEIVE] Payload: %s\n", msg.payload);
        printf("----------------------------------------\n");

        g_message_callback(&msg);
    }
}


// MQTT循环处理线程
static void* mqtt_loop_thread(void* arg) {
    (void)arg;

    while (g_loop_running) {
        // 在A7开发板上取消注释以下代码：
        
        int ret = mosquitto_loop(g_mosq, 1000, 1);
        if (ret != MOSQ_ERR_SUCCESS) {
            if (g_loop_running) {
                print_error(__func__, "MQTT loop error", ERROR_MQTT_CONNECT);
                g_mqtt_status = MQTT_ERROR;
            }
            break;
        }
        
        sleep(1); // 临时实现，在A7开发板上删除此行
    }

    return NULL;
}

int mqtt_init(void) {
    // 在A7开发板上取消注释以下代码：
    
    // 初始化libmosquitto库
    mosquitto_lib_init();

    // 创建MQTT客户端实例
    g_mosq = mosquitto_new(MQTT_CLIENT_ID, true, NULL);
    if (!g_mosq) {
        print_error(__func__, "Failed to create MQTT client", ERROR_MQTT_CONNECT);
        return ERROR_MQTT_CONNECT;
    }

    // 设置回调函数
    mosquitto_connect_callback_set(g_mosq, on_connect);
    mosquitto_disconnect_callback_set(g_mosq, on_disconnect);
    mosquitto_message_callback_set(g_mosq, on_message);
    

    printf("[INFO] MQTT client initialized\n");
    printf("[INFO] Server: %s:%d\n", MQTT_HOST, MQTT_PORT);
    printf("[INFO] Client ID: %s\n", MQTT_CLIENT_ID);
    printf("[INFO] Publish Topic: %s\n", MQTT_PUB_TOPIC);
    printf("[INFO] Subscribe Topic: %s\n", MQTT_SUB_TOPIC);

    return SUCCESS;
}

int mqtt_connect(void) {
    printf("[INFO] Connecting to MQTT broker %s:%d...\n", MQTT_HOST, MQTT_PORT);
    g_mqtt_status = MQTT_CONNECTING;

    // 在A7开发板上取消注释以下代码：
    
    if (!g_mosq) {
        print_error(__func__, "MQTT client not initialized", ERROR_MQTT_CONNECT);
        return ERROR_MQTT_CONNECT;
    }

    int ret = mosquitto_connect(g_mosq, MQTT_HOST, MQTT_PORT, MQTT_KEEPALIVE);
    if (ret != MOSQ_ERR_SUCCESS) {
        g_mqtt_status = MQTT_ERROR;
        print_error(__func__, "Failed to connect to MQTT broker", ERROR_MQTT_CONNECT);
        return ERROR_MQTT_CONNECT;
    }
    

    // 临时实现 - 在A7开发板上删除此行
    g_mqtt_status = MQTT_CONNECTED;
    printf("[INFO] MQTT connected successfully\n");

    return SUCCESS;
}

int mqtt_disconnect(void) {
    printf("[INFO] Disconnecting from MQTT broker...\n");

    // 在A7开发板上取消注释以下代码：
    
    if (!g_mosq) {
        return SUCCESS;
    }
    mosquitto_disconnect(g_mosq);
    

    g_mqtt_status = MQTT_DISCONNECTED;
    printf("[INFO] MQTT disconnected\n");
    return SUCCESS;
}

int mqtt_publish(const char* topic, const char* payload, int payload_len) {
    if (!topic || !payload) {
        print_error(__func__, "Invalid parameters", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    if (g_mqtt_status != MQTT_CONNECTED) {
        print_error(__func__, "MQTT not connected", ERROR_MQTT_CONNECT);
        return ERROR_MQTT_CONNECT;
    }

    // 在A7开发板上取消注释以下代码：
    
    int ret = mosquitto_publish(g_mosq, NULL, topic, payload_len, payload, 0, false);
    if (ret != MOSQ_ERR_SUCCESS) {
        print_error(__func__, "Failed to publish message", ERROR_MQTT_CONNECT);
        return ERROR_MQTT_CONNECT;
    }
    

    printf("[MQTT PUBLISH] Topic: %s\n", topic);
    printf("[MQTT PUBLISH] Payload (%d bytes): %.*s\n", payload_len, payload_len, payload);
    printf("----------------------------------------\n");

    return SUCCESS;
}

int mqtt_subscribe(const char* topic) {
    if (!topic) {
        print_error(__func__, "Invalid parameter", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    if (g_mqtt_status != MQTT_CONNECTED) {
        print_error(__func__, "MQTT not connected", ERROR_MQTT_CONNECT);
        return ERROR_MQTT_CONNECT;
    }

    // 在A7开发板上取消注释以下代码：
    
    int ret = mosquitto_subscribe(g_mosq, NULL, topic, 0);
    if (ret != MOSQ_ERR_SUCCESS) {
        print_error(__func__, "Failed to subscribe to topic", ERROR_MQTT_CONNECT);
        return ERROR_MQTT_CONNECT;
    }
    

    printf("[INFO] Subscribed to topic: %s\n", topic);
    return SUCCESS;
}

int mqtt_unsubscribe(const char* topic) {
    if (!topic) {
        print_error(__func__, "Invalid parameter", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    // 在A7开发板上取消注释以下代码：
    
    if (!g_mosq) {
        return SUCCESS;
    }

    int ret = mosquitto_unsubscribe(g_mosq, NULL, topic);
    if (ret != MOSQ_ERR_SUCCESS) {
        print_error(__func__, "Failed to unsubscribe from topic", ERROR_MQTT_CONNECT);
        return ERROR_MQTT_CONNECT;
    }
    

    printf("[INFO] Unsubscribed from topic: %s\n", topic);
    return SUCCESS;
}

int mqtt_loop_start(void) {
    if (g_loop_running) {
        printf("[WARNING] MQTT loop already running\n");
        return SUCCESS;
    }

    g_loop_running = 1;
    
    if (pthread_create(&g_loop_thread, NULL, mqtt_loop_thread, NULL) != 0) {
        print_error(__func__, "Failed to create MQTT loop thread", ERROR_MQTT_CONNECT);
        g_loop_running = 0;
        return ERROR_MQTT_CONNECT;
    }

    printf("[INFO] MQTT loop thread started\n");
    return SUCCESS;
}

int mqtt_loop_stop(void) {
    if (!g_loop_running) {
        return SUCCESS;
    }

    g_loop_running = 0;
    
    if (pthread_join(g_loop_thread, NULL) != 0) {
        print_error(__func__, "Failed to join MQTT loop thread", ERROR_MQTT_CONNECT);
        return ERROR_MQTT_CONNECT;
    }

    printf("[INFO] MQTT loop thread stopped\n");
    return SUCCESS;
}

int mqtt_loop(void) {
    // 在A7开发板上取消注释以下代码：
    
    if (!g_mosq) {
        return ERROR_MQTT_CONNECT;
    }

    return mosquitto_loop(g_mosq, 1000, 1) == MOSQ_ERR_SUCCESS ? SUCCESS : ERROR_MQTT_CONNECT;
    

    return SUCCESS; // 临时实现
}

void mqtt_set_message_callback(mqtt_message_callback_t callback) {
    g_message_callback = callback;
}

mqtt_status_t mqtt_get_status(void) {
    return g_mqtt_status;
}

void mqtt_cleanup(void) {
    mqtt_loop_stop();
    mqtt_disconnect();

    // 在A7开发板上取消注释以下代码：
    
    if (g_mosq) {
        mosquitto_destroy(g_mosq);
        g_mosq = NULL;
    }

    mosquitto_lib_cleanup();
    

    printf("[INFO] MQTT client cleaned up\n");
}

int mqtt_publish_sensor_data(const char* json_data) {
    if (!json_data) {
        print_error(__func__, "Invalid parameter", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }
    
    return mqtt_publish(MQTT_PUB_TOPIC, json_data, strlen(json_data));
}

int mqtt_publish_response(const char* json_response) {
    if (!json_response) {
        print_error(__func__, "Invalid parameter", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }
    
    return mqtt_publish(MQTT_PUB_TOPIC, json_response, strlen(json_response));
}
