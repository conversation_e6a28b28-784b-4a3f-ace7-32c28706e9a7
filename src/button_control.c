#include "button_control.h"
#include "actuators.h"
#include "file_utils.h"
#include <stdio.h>
#include <unistd.h>
#include <time.h>

// 条件编译：检查是否有GPIO库支持
#ifdef ENABLE_GPIO_BUTTONS
#include <gpiod.h>
#define GPIO_AVAILABLE 1
#else
#define GPIO_AVAILABLE 0
#endif

/**
 * @file button_control.c
 * @brief 智能家居系统按键控制模块实现
 * @details 集成风扇、灯光和主电源的GPIO按键控制功能
 *          使用libgpiod库进行GPIO操作，包含按键防抖处理
 * <AUTHOR> Home System Team
 * @version 1.0.0
 */

// GPIO资源（仅在GPIO可用时使用）
#if GPIO_AVAILABLE
static struct gpiod_chip* g_gpio_chip = NULL;
static struct gpiod_line* g_key1_line = NULL;  // 灯光控制
static struct gpiod_line* g_key2_line = NULL;  // 主电源控制
static struct gpiod_line* g_key3_line = NULL;  // 风扇控制
#endif

// 按键状态
static fan_button_state_t g_fan_state = FAN_BUTTON_OFF;
static light_button_mode_t g_light_mode = LIGHT_MODE_ALL_OFF;
static main_power_button_state_t g_main_power_state = MAIN_POWER_BUTTON_OFF;

// 按键防抖相关
static time_t g_last_key1_press = 0;
static time_t g_last_key2_press = 0;
static time_t g_last_key3_press = 0;
#define DEBOUNCE_DELAY_MS 200  // 防抖延时200ms

// 内部辅助函数：控制单个LED
static int button_led_control(int led_num, int state) {
    char path[100];
    snprintf(path, sizeof(path), "/sys/class/leds/led%d/brightness", led_num);
    
    FILE *fp = fopen(path, "w");
    if (fp == NULL) {
        return ERROR_FILE_OPEN;
    }
    
    fprintf(fp, "%d", state);
    fclose(fp);
    return SUCCESS;
}

// 内部辅助函数：控制风扇
static int button_fan_control(int state) {
    char path[100];
    snprintf(path, sizeof(path), "/sys/class/hwmon/hwmon1/pwm1");
    
    FILE *fp = fopen(path, "w");
    if (fp == NULL) {
        return ERROR_FILE_OPEN;
    }
    
    if (state == 0) {
        fprintf(fp, "0");    // 关闭风扇
    } else {
        fprintf(fp, "255");  // 全速开启风扇
    }
    fclose(fp);
    return SUCCESS;
}

// 内部辅助函数：关闭所有LED
static void button_turn_off_all_leds(void) {
    for (int i = 1; i <= 5; i++) {
        button_led_control(i, 0);
    }
}

// 内部辅助函数：点亮指定LED，其他熄灭
static void button_set_single_led(int led_num) {
    button_turn_off_all_leds();
    if (led_num >= 1 && led_num <= 5) {
        button_led_control(led_num, 1);
    }
}

// 内部辅助函数：控制所有设备
static void button_control_all_devices(int state) {
    // 控制5个LED灯
    for (int i = 1; i <= 5; i++) {
        button_led_control(i, state);
    }
    
    // 控制风扇
    button_fan_control(state);
    
    if (state == 0) {
        printf("[BUTTON] All devices OFF\n");
    } else {
        printf("[BUTTON] All devices ON\n");
    }
}

// 获取当前时间（毫秒）
static long long get_current_time_ms(void) {
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return (long long)(ts.tv_sec * 1000 + ts.tv_nsec / 1000000);
}

int button_control_init(void) {
    printf("[INFO] Initializing button control module...\n");

#if GPIO_AVAILABLE
    // 打开GPIO芯片
    g_gpio_chip = gpiod_chip_open_by_label(GPIO_CHIP_LABEL);
    if (!g_gpio_chip) {
        print_error(__func__, "Failed to open GPIO chip", ERROR_FILE_OPEN);
        return ERROR_FILE_OPEN;
    }

    // 获取KEY1引脚 (灯光控制)
    g_key1_line = gpiod_chip_get_line(g_gpio_chip, KEY1_PIN);
    if (!g_key1_line) {
        print_error(__func__, "Failed to get KEY1 line", ERROR_FILE_OPEN);
        return ERROR_FILE_OPEN;
    }

    // 获取KEY2引脚 (主电源控制)
    g_key2_line = gpiod_chip_get_line(g_gpio_chip, KEY2_PIN);
    if (!g_key2_line) {
        print_error(__func__, "Failed to get KEY2 line", ERROR_FILE_OPEN);
        return ERROR_FILE_OPEN;
    }

    // 获取KEY3引脚 (风扇控制)
    g_key3_line = gpiod_chip_get_line(g_gpio_chip, KEY3_PIN);
    if (!g_key3_line) {
        print_error(__func__, "Failed to get KEY3 line", ERROR_FILE_OPEN);
        return ERROR_FILE_OPEN;
    }

    // 设置所有引脚为输入模式
    if (gpiod_line_request_input(g_key1_line, "key1") != 0 ||
        gpiod_line_request_input(g_key2_line, "key2") != 0 ||
        gpiod_line_request_input(g_key3_line, "key3") != 0) {
        print_error(__func__, "Failed to request GPIO lines as input", ERROR_FILE_OPEN);
        return ERROR_FILE_OPEN;
    }

    printf("[INFO] Button control initialized successfully\n");
    printf("[INFO] KEY1(PF%d): Light control\n", KEY1_PIN);
    printf("[INFO] KEY2(PF%d): Main power control\n", KEY2_PIN);
    printf("[INFO] KEY3(PF%d): Fan control\n", KEY3_PIN);
#else
    printf("[INFO] Button control initialized in simulation mode (GPIO not available)\n");
    printf("[INFO] Physical buttons will not work, but software interface is available\n");
#endif

    // 初始化时关闭所有设备
    button_control_all_devices(0);

    return SUCCESS;
}

int button_check_fan_key(void) {
#if GPIO_AVAILABLE
    if (g_key3_line) {
        int status = gpiod_line_get_value(g_key3_line);
        // 按键按下时返回0（低电平），未按下时返回1（高电平）
        return (status == 0) ? 1 : 0;
    }
#endif
    return 0;
}

int button_check_light_key(void) {
#if GPIO_AVAILABLE
    if (g_key1_line) {
        int status = gpiod_line_get_value(g_key1_line);
        return (status == 0) ? 1 : 0;
    }
#endif
    return 0;
}

int button_check_main_power_key(void) {
#if GPIO_AVAILABLE
    if (g_key2_line) {
        int status = gpiod_line_get_value(g_key2_line);
        return (status == 0) ? 1 : 0;
    }
#endif
    return 0;
}

void button_handle_fan_toggle(void) {
    if (g_fan_state == FAN_BUTTON_OFF) {
        g_fan_state = FAN_BUTTON_ON;
        button_fan_control(1);
        printf("[BUTTON] Fan ON\n");
    } else {
        g_fan_state = FAN_BUTTON_OFF;
        button_fan_control(0);
        printf("[BUTTON] Fan OFF\n");
    }
}

void button_handle_light_toggle(void) {
    // 切换到下一个模式
    g_light_mode = (g_light_mode + 1) % LIGHT_MODE_MAX;

    // 根据模式控制灯的状态
    switch(g_light_mode) {
        case LIGHT_MODE_ALL_OFF:
            button_turn_off_all_leds();
            printf("[BUTTON] All lights off\n");
            break;
        case LIGHT_MODE_1_ONLY:
            button_set_single_led(1);
            printf("[BUTTON] Light 1 on, others off\n");
            break;
        case LIGHT_MODE_2_ONLY:
            button_set_single_led(2);
            printf("[BUTTON] Light 2 on, others off\n");
            break;
        case LIGHT_MODE_3_ONLY:
            button_set_single_led(3);
            printf("[BUTTON] Light 3 on, others off\n");
            break;
        case LIGHT_MODE_4_ONLY:
            button_set_single_led(4);
            printf("[BUTTON] Light 4 on, others off\n");
            break;
        case LIGHT_MODE_5_ONLY:
            button_set_single_led(5);
            printf("[BUTTON] Light 5 on, others off\n");
            break;
        default:
            break;
    }
}

void button_handle_main_power_toggle(void) {
    if (g_main_power_state == MAIN_POWER_BUTTON_OFF) {
        g_main_power_state = MAIN_POWER_BUTTON_ON;
        button_control_all_devices(1);
        printf("[BUTTON] Main power ON\n");
    } else {
        g_main_power_state = MAIN_POWER_BUTTON_OFF;
        button_control_all_devices(0);
        printf("[BUTTON] Main power OFF\n");
    }
}

fan_button_state_t button_get_fan_state(void) {
    return g_fan_state;
}

light_button_mode_t button_get_light_mode(void) {
    return g_light_mode;
}

main_power_button_state_t button_get_main_power_state(void) {
    return g_main_power_state;
}

void button_scan_and_handle(void) {
    long long current_time = get_current_time_ms();

    // 检查KEY1（灯光控制）
    if (button_check_light_key()) {
        if (current_time - g_last_key1_press > DEBOUNCE_DELAY_MS) {
            button_handle_light_toggle();
            g_last_key1_press = current_time;
        }
    }

    // 检查KEY2（主电源控制）
    if (button_check_main_power_key()) {
        if (current_time - g_last_key2_press > DEBOUNCE_DELAY_MS) {
            button_handle_main_power_toggle();
            g_last_key2_press = current_time;
        }
    }

    // 检查KEY3（风扇控制）
    if (button_check_fan_key()) {
        if (current_time - g_last_key3_press > DEBOUNCE_DELAY_MS) {
            button_handle_fan_toggle();
            g_last_key3_press = current_time;
        }
    }
}

void button_control_cleanup(void) {
    printf("[INFO] Cleaning up button control resources...\n");

#if GPIO_AVAILABLE
    // 释放GPIO线路
    if (g_key1_line) {
        gpiod_line_release(g_key1_line);
        g_key1_line = NULL;
    }
    if (g_key2_line) {
        gpiod_line_release(g_key2_line);
        g_key2_line = NULL;
    }
    if (g_key3_line) {
        gpiod_line_release(g_key3_line);
        g_key3_line = NULL;
    }

    // 关闭GPIO芯片
    if (g_gpio_chip) {
        gpiod_chip_close(g_gpio_chip);
        g_gpio_chip = NULL;
    }
#endif

    // 退出时关闭所有设备
    button_control_all_devices(0);

    printf("[INFO] Button control cleanup completed\n");
}
