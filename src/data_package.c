#include "data_package.h"
#include "file_utils.h"
#include <stdio.h>
#include <string.h>
#include <time.h>

int package_sensor_data_to_json(const sensor_data_t* data, char* json_buffer, size_t buffer_size) {
    if (!data || !json_buffer || buffer_size == 0) {
        print_error(__func__, "Invalid parameters", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    // 根据M4规范构建JSON字符串
    // 温湿度传感器：{"tem":float,"hum":float,"id":int}
    // 光照传感器：{"light":float,"id":int}
    // 电压传感器：{"voltage":float,"id":int} (这里用作电流传感器的电压读数)
    int ret = snprintf(json_buffer, buffer_size,
        "{"
        "\"tem\":%.2f,\"hum\":%.2f,\"id\":0,"
        "\"light\":%.2f,\"id\":1,"
        "\"voltage\":%.2f,\"id\":3"
        "}",
        data->temperature,
        data->humidity,
        (float)data->light_intensity,
        data->voltage
    );

    if (ret >= (int)buffer_size) {
        print_error(__func__, "JSON buffer too small", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    return SUCCESS;
}

int package_complete_data_to_json(const sensor_data_t* sensor_data,
                                  const fan_status_t* fan_status,
                                  char* json_buffer,
                                  size_t buffer_size) {
    if (!sensor_data || !fan_status || !json_buffer || buffer_size == 0) {
        print_error(__func__, "Invalid parameters", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    // 根据M4规范构建完整的JSON字符串
    // 传感器数据：温湿度传感器(id:0), 光照传感器(id:1), 电压传感器(id:3)
    // 执行器数据：风扇(id:0)
    int ret = snprintf(json_buffer, buffer_size,
        "{"
        "\"tem\":%.2f,\"hum\":%.2f,\"id\":0,"
        "\"light\":%.2f,\"id\":1,"
        "\"voltage\":%.2f,\"id\":3,"
        "\"fan\":%s,\"id\":0"
        "}",
        sensor_data->temperature,
        sensor_data->humidity,
        (float)sensor_data->light_intensity,
        sensor_data->voltage,
        fan_status->is_running ? "true" : "false"
    );

    if (ret >= (int)buffer_size) {
        print_error(__func__, "JSON buffer too small", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    return SUCCESS;
}

int parse_control_command(const char* json_str, int* fan_command, int* led_command, int* buzzer_command) {
    if (!json_str || !fan_command || !led_command || !buzzer_command) {
        print_error(__func__, "Invalid parameters", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    // 初始化输出参数
    *fan_command = -1;    // -1表示无指令
    *led_command = -1;
    *buzzer_command = -1;

    // 根据M4规范解析JSON控制指令
    // 风扇控制格式：{"fan":bool,"id":int}
    // LED控制格式：{"lamp":bool,"id":int}

    char* fan_pos = strstr(json_str, "\"fan\":");
    if (fan_pos) {
        char* value_start = strchr(fan_pos, ':');
        if (value_start) {
            value_start++; // 跳过冒号
            while (*value_start == ' ' || *value_start == '\t') value_start++; // 跳过空白字符

            if (strncmp(value_start, "true", 4) == 0) {
                *fan_command = FAN_SPEED_MEDIUM; // 默认中速
            } else if (strncmp(value_start, "false", 5) == 0) {
                *fan_command = FAN_SPEED_OFF;
            }
        }
    }

    // 检查lamp字段（LED控制）
    char* lamp_pos = strstr(json_str, "\"lamp\":");
    if (lamp_pos) {
        char* value_start = strchr(lamp_pos, ':');
        if (value_start) {
            value_start++; // 跳过冒号
            while (*value_start == ' ' || *value_start == '\t') value_start++; // 跳过空白字符

            if (strncmp(value_start, "true", 4) == 0) {
                *led_command = 1; // 开启/闪烁
            } else if (strncmp(value_start, "false", 5) == 0) {
                *led_command = 0; // 关闭
            }
        }
    }

    // 兼容旧格式的led字段
    char* led_pos = strstr(json_str, "\"led\":");
    if (led_pos && *led_command == -1) {
        char* value_start = strchr(led_pos, ':');
        if (value_start) {
            value_start++; // 跳过冒号
            while (*value_start == ' ' || *value_start == '\t') value_start++; // 跳过空白字符

            if (strncmp(value_start, "true", 4) == 0 || strncmp(value_start, "\"on\"", 4) == 0) {
                *led_command = 1; // 开启/闪烁
            } else if (strncmp(value_start, "false", 5) == 0 || strncmp(value_start, "\"off\"", 5) == 0) {
                *led_command = 0; // 关闭
            }
        }
    }

    // 检查buzzer字段（蜂鸣器控制）
    char* buzzer_pos = strstr(json_str, "\"buzzer\":");
    if (buzzer_pos) {
        char* value_start = strchr(buzzer_pos, ':');
        if (value_start) {
            value_start++; // 跳过冒号
            while (*value_start == ' ' || *value_start == '\t') value_start++; // 跳过空白字符

            if (strncmp(value_start, "true", 4) == 0) {
                *buzzer_command = BUZZER_FREQ_MEDIUM; // 默认中频
            } else if (strncmp(value_start, "false", 5) == 0) {
                *buzzer_command = BUZZER_OFF; // 关闭
            } else if (*value_start >= '0' && *value_start <= '9') {
                // 尝试解析数字频率值
                *buzzer_command = atoi(value_start);
                if (*buzzer_command < 0) {
                    *buzzer_command = BUZZER_OFF;
                }
            } else if (strncmp(value_start, "\"on\"", 4) == 0) {
                *buzzer_command = BUZZER_FREQ_MEDIUM; // 兼容旧格式
            } else if (strncmp(value_start, "\"off\"", 5) == 0) {
                *buzzer_command = BUZZER_OFF; // 兼容旧格式
            }
        }
    }

    return SUCCESS;
}

int create_response_json(int success, const char* message, char* json_buffer, size_t buffer_size) {
    if (!json_buffer || buffer_size == 0) {
        print_error(__func__, "Invalid parameters", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    const char* msg = message ? message : (success ? "OK" : "ERROR");
    
    // 获取当前时间字符串
    time_t now = time(NULL);
    struct tm* tm_info = localtime(&now);
    char time_str[64];
    strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", tm_info);

    int ret = snprintf(json_buffer, buffer_size,
        "{"
        "\"timestamp\":\"%s\","
        "\"device_id\":\"%s\","
        "\"status\":\"%s\","
        "\"message\":\"%s\""
        "}",
        time_str,
        MQTT_CLIENT_ID,
        success ? "success" : "error",
        msg
    );

    if (ret >= (int)buffer_size) {
        print_error(__func__, "JSON buffer too small", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    return SUCCESS;
}
