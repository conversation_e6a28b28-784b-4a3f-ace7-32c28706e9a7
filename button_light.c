#include <gpiod.h>
#include <stdio.h>
#include <unistd.h>
#include <string.h>

#define GPIO_CHIP_LABEL "GPIOF"
#define KEY1_PIN 9  // PF9

// 灯光模式枚举
typedef enum {
    ALL_OFF = 0,     // 全灭
    LIGHT1_ONLY,     // 灯1亮
    LIGHT2_ONLY,     // 灯2亮
    LIGHT3_ONLY,     // 灯3亮
    LIGHT4_ONLY,     // 灯4亮
    LIGHT5_ONLY,     // 灯5亮
    LIGHT_MODE_MAX
} light_mode_t;

static light_mode_t current_light_mode = ALL_OFF;
static struct gpiod_chip* gpio_chip = NULL;
static struct gpiod_line* key1_line = NULL;

// 控制单个LED的亮灭
// led_num: LED编号
// state: 1=亮, 0=灭
int led_control(int led_num, int state) {
    char path[100];
    char buffer[10];
    // 构建LED控制文件路径
    snprintf(path, sizeof(path), "/sys/class/leds/led%d/brightness", led_num);
    
    FILE *fp = fopen(path, "w");
    if (fp == NULL) {
        printf("无法打开LED%d控制文件: %s\n", led_num, path);
        return -1;
    }
    
    // 写入状态
    fprintf(fp, "%d", state);
    fclose(fp);
    return 0;
}

// 熄灭所有LED
void turn_off_all_leds() {
    for (int i = 1; i <= 5; i++) {
        led_control(i, 0);
    }
}

// 点亮指定LED，其他熄灭
void set_single_led(int led_num) {
    turn_off_all_leds();
    if (led_num >= 1 && led_num <= 5) {
        led_control(led_num, 1);
    }
}

// 灯光初始化函数
int light_init(void) {
    // 打开GPIO芯片
    gpio_chip = gpiod_chip_open_by_label(GPIO_CHIP_LABEL);
    if (!gpio_chip) {
        printf("open gpiochip failed\n");
        return -1;
    }
    
    // 获取KEY1引脚PF9
    key1_line = gpiod_chip_get_line(gpio_chip, KEY1_PIN);
    if (!key1_line) {
        printf("get key1 line failed\n");
        return -1;
    }
    
    // 将GPIO引脚设置为输入
    if (gpiod_line_request_input(key1_line, "key1") != 0) {
        printf("request key1 line failed\n");
        return -1;
    }
    
    // 初始化：确保所有LED开始时都是熄灭状态
    turn_off_all_leds();
    
    printf("Light control initialized, all lights off\n");
    return 0;
}

// 灯光模式切换函数
void light_toggle_mode(void) {
    // 切换到下一个模式
    current_light_mode = (current_light_mode + 1) % LIGHT_MODE_MAX;
    
    // 根据模式控制灯的状态
    switch(current_light_mode) {
        case ALL_OFF:
            turn_off_all_leds();
            printf("All lights off\n");
            break;
        case LIGHT1_ONLY:
            set_single_led(1);
            printf("Light 1 on, others off\n");
            break;
        case LIGHT2_ONLY:
            set_single_led(2);
            printf("Light 2 on, others off\n");
            break;
        case LIGHT3_ONLY:
            set_single_led(3);
            printf("Light 3 on, others off\n");
            break;
        case LIGHT4_ONLY:
            set_single_led(4);
            printf("Light 4 on, others off\n");
            break;
        case LIGHT5_ONLY:
            set_single_led(5);
            printf("Light 5 on, others off\n");
            break;
        default:
            break;
    }
}

// 获取当前灯光模式
light_mode_t light_get_mode(void) {
    return current_light_mode;
}

// 检测按键是否按下
int light_check_key_press(void) {
    if (key1_line) {
        int status = gpiod_line_get_value(key1_line);
        // 按键按下时返回0低电平，未按下时返回1高电平
        return (status == 0) ? 1 : 0;
    }
    return 0;
}

// 释放资源
void light_release(void) {
    if (key1_line) {
        gpiod_line_release(key1_line);
    }
    if (gpio_chip) {
        gpiod_chip_close(gpio_chip);
    }
    
    // 退出时关闭所有LED
    turn_off_all_leds();
}