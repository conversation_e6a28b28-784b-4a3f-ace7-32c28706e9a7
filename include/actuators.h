#ifndef ACTUATORS_H
#define ACTUATORS_H

#include "smart_home_config.h"

/**
 * @file actuators.h
 * @brief 智能家居系统执行器模块接口定义
 * @details 本模块提供风扇控制和LED控制的统一接口
 *          直接控制Linux sysfs文件系统中的硬件设备
 * <AUTHOR> Home System Team
 * @version 1.0.0
 */

// 风扇速度等级枚举
typedef enum {
    FAN_OFF = 0,    // 风扇关闭
    FAN_LOW,        // 低速 (对应PWM值100)
    FAN_MEDIUM,     // 中速 (对应PWM值180)
    FAN_HIGH        // 高速 (对应PWM值255)
} fan_speed_level_t;

// 风扇状态结构体
typedef struct {
    int is_running;              // 风扇是否正在运行 (0=停止, 1=运行)
    int current_speed;           // 当前PWM速度值 (0-255)
    fan_speed_level_t level;     // 当前速度等级
} fan_status_t;

/**
 * @brief 初始化所有执行器模块
 * @details 检查风扇控制文件是否存在，初始化时关闭所有设备
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 初始化成功
 * @retval ERROR_FILE_OPEN 风扇控制文件不存在或无法访问
 */
int actuators_init(void);

/**
 * @brief 设置风扇PWM速度值
 * @details 直接向/sys/class/hwmon/hwmon1/pwm1写入速度值
 *          同时更新全局风扇状态
 * @param[in] speed PWM速度值，范围0-255 (0=停止, 255=最高速)
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 设置成功
 * @retval ERROR_INVALID_PARAM 速度值超出范围(0-255)
 * @retval ERROR_FILE_WRITE 文件写入失败
 */
int set_fan_speed(int speed);

/**
 * @brief 设置风扇速度等级
 * @details 根据等级枚举设置对应的PWM值
 *          FAN_OFF=0, FAN_LOW=100, FAN_MEDIUM=180, FAN_HIGH=255
 * @param[in] level 风扇速度等级枚举值
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 设置成功
 * @retval ERROR_INVALID_PARAM 无效的等级值
 */
int set_fan_level(fan_speed_level_t level);

/**
 * @brief 开启风扇到指定等级
 * @details 如果传入FAN_OFF，则默认设置为FAN_MEDIUM
 * @param[in] level 目标速度等级，FAN_OFF时默认为FAN_MEDIUM
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 */
int fan_on(fan_speed_level_t level);

/**
 * @brief 关闭风扇
 * @details 设置PWM值为0，停止风扇运行
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 */
int fan_off(void);

/**
 * @brief 获取当前风扇状态
 * @details 返回全局维护的风扇状态信息
 * @param[out] status 输出参数，存储风扇状态结构体
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 获取成功
 * @retval ERROR_INVALID_PARAM 参数为空指针
 */
int get_fan_status(fan_status_t* status);

/**
 * @brief 格式化打印风扇状态
 * @details 以易读格式输出风扇运行状态、速度值和等级
 * @param[in] status 要打印的风扇状态结构体指针
 * @note 如果status为NULL，会输出错误信息
 */
void print_fan_status(const fan_status_t* status);

/**
 * @brief 控制LED闪烁
 * @details 同时控制LED1、LED2、LED3进行同步闪烁
 *          每次闪烁包含开启-等待-关闭-等待的完整周期
 * @param[in] times 闪烁次数，必须大于0
 * @param[in] interval_ms 每次开启/关闭的持续时间(毫秒)，必须大于0
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 闪烁完成
 * @retval ERROR_INVALID_PARAM 参数无效(次数或间隔<=0)
 */
int led_blink(int times, int interval_ms);

/**
 * @brief 设置单个LED状态
 * @details 控制指定编号的LED开启或关闭
 *          LED路径: LED1=/sys/class/leds/led1/brightness
 *                  LED2=/sys/class/leds/led2/brightness
 *                  LED3=/sys/class/leds/led3/brightness
 * @param[in] led_num LED编号 (1=LED1, 2=LED2, 3=LED3)
 * @param[in] state LED状态 (0=关闭, 非0=开启)
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 设置成功
 * @retval ERROR_INVALID_PARAM LED编号无效(不在1-3范围内)
 * @retval ERROR_FILE_WRITE 文件写入失败
 */
int set_led_state(int led_num, int state);

/**
 * @brief 关闭所有LED
 * @details 依次关闭LED1、LED2、LED3
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 所有LED关闭成功
 * @retval 其他错误码 某个LED关闭失败时的错误码
 */
int led_all_off(void);

/**
 * @brief 设置蜂鸣器频率
 * @details 通过Linux input子系统控制蜂鸣器发声
 *          向/dev/input/by-path/platform-beeper-event写入input_event事件
 * @param[in] frequency 蜂鸣器频率(Hz)，0表示关闭蜂鸣器，非0值表示发声频率
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 设置成功
 * @retval ERROR_FILE_OPEN 蜂鸣器设备文件打开失败
 * @retval ERROR_FILE_WRITE 写入设备文件失败
 */
int set_buzzer_frequency(unsigned int frequency);

/**
 * @brief 蜂鸣器发出指定频率的声音持续指定时间
 * @details 开启蜂鸣器指定时间后自动关闭
 * @param[in] frequency 蜂鸣器频率(Hz)，建议使用配置文件中定义的频率常量
 * @param[in] duration_ms 持续时间(毫秒)，必须大于0
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 蜂鸣完成
 * @retval ERROR_INVALID_PARAM 参数无效(持续时间<=0)
 */
int buzzer_beep(unsigned int frequency, int duration_ms);

/**
 * @brief 蜂鸣器发出报警声
 * @details 使用预定义的报警频率(1500Hz)发声指定时间
 * @param[in] duration_ms 持续时间(毫秒)，必须大于0
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 */
int buzzer_alarm(int duration_ms);

/**
 * @brief 关闭蜂鸣器
 * @details 设置频率为0，停止蜂鸣器发声
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 */
int buzzer_off(void);

#endif // ACTUATORS_H
