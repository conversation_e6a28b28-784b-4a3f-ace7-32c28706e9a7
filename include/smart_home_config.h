#ifndef SMART_HOME_CONFIG_H
#define SMART_HOME_CONFIG_H

/**
 * @file smart_home_config.h
 * @brief 智能家居系统全局配置参数定义
 * @details 本文件包含系统所有配置参数，包括硬件路径、网络配置、
 *          控制阈值等，便于统一管理和修改
 * <AUTHOR> Home System Team
 * @version 1.0.0
 */

// ============================================================================
// 系统基本信息配置
// ============================================================================
#define SYSTEM_VERSION "1.0.0"              // 系统版本号
#define SYSTEM_NAME "Smart Home System"     // 系统名称

// ============================================================================
// 传感器设备路径配置 (Linux sysfs路径)
// ============================================================================
#define TEMP_HUMIDITY_DEVICE_PATH "/sys/bus/iio/devices/iio:device0"  // 温湿度传感器设备路径
#define LIGHT_SENSOR_DEVICE_PATH "/sys/bus/iio/devices/iio:device1"   // 光照传感器设备路径
#define CURRENT_SENSOR_DEVICE_PATH "/sys/bus/iio/devices/iio:device3" // 电流传感器设备路径(实际为电压ADC)

// ============================================================================
// 执行器设备路径配置 (Linux sysfs路径)
// ============================================================================
#define FAN_CONTROL_PATH "/sys/class/hwmon/hwmon1/pwm1"               // 风扇PWM控制文件路径

// LED控制文件路径
#define LED1_PATH "/sys/class/leds/led1/brightness"                   // LED1亮度控制文件
#define LED2_PATH "/sys/class/leds/led2/brightness"                   // LED2亮度控制文件
#define LED3_PATH "/sys/class/leds/led3/brightness"                   // LED3亮度控制文件

// 蜂鸣器控制文件路径
#define BUZZER_DEVICE_PATH "/dev/input/by-path/platform-beeper-event" // 蜂鸣器设备文件路径

// ============================================================================
// MQTT网络通信配置参数
// ============================================================================
#define MQTT_HOST "mqtt.yyzlab.com.cn"                               // MQTT服务器地址
#define MQTT_PORT 1883                                               // MQTT服务器端口(标准端口)
#define MQTT_CLIENT_ID "kmlg_a7_board_01"                           // MQTT客户端唯一标识
#define MQTT_PUB_TOPIC "1756615186765/Device2AIOTSIM"               // 数据发布主题(设备->云端)
#define MQTT_SUB_TOPIC "1756615186765/AIOTSIM2Device"               // 指令订阅主题(云端->设备)
#define MQTT_KEEPALIVE 60                                            // MQTT连接保活时间(秒)

// ============================================================================
// 系统运行时间参数配置
// ============================================================================
#define DATA_COLLECTION_INTERVAL 5                                   // 传感器数据采集间隔(秒)
#define MQTT_PUBLISH_INTERVAL 10                                     // MQTT数据发布间隔(秒)

// ============================================================================
// 自动控制逻辑阈值配置
// ============================================================================
#define TEMP_THRESHOLD 28.0                                          // 风扇启动温度阈值(°C)
#define HUMIDITY_THRESHOLD 70.0                                      // 湿度警告阈值(%RH)
#define LIGHT_THRESHOLD 100                                          // 光照阈值(lux，预留)

// ============================================================================
// 风扇PWM速度等级配置 (0-255范围)
// ============================================================================
#define FAN_SPEED_OFF 0                                              // 风扇关闭(PWM=0)
#define FAN_SPEED_LOW 100                                            // 风扇低速(PWM=100)
#define FAN_SPEED_MEDIUM 180                                         // 风扇中速(PWM=180)
#define FAN_SPEED_HIGH 255                                           // 风扇高速(PWM=255)

// ============================================================================
// 蜂鸣器频率配置 (Hz)
// ============================================================================
#define BUZZER_OFF 0                                                 // 蜂鸣器关闭
#define BUZZER_FREQ_LOW 500                                          // 低频音调(500Hz)
#define BUZZER_FREQ_MEDIUM 1000                                      // 中频音调(1000Hz)
#define BUZZER_FREQ_HIGH 2000                                        // 高频音调(2000Hz)
#define BUZZER_FREQ_ALARM 1500                                       // 报警音调(1500Hz)



// ============================================================================
// 系统错误码定义 (负数表示错误，0表示成功)
// ============================================================================
#define SUCCESS 0                                                    // 操作成功
#define ERROR_FILE_OPEN -1                                           // 文件打开失败
#define ERROR_FILE_READ -2                                           // 文件读取失败
#define ERROR_FILE_WRITE -3                                          // 文件写入失败
#define ERROR_INVALID_PARAM -4                                       // 参数无效(空指针等)
#define ERROR_MQTT_CONNECT -5                                        // MQTT连接失败
#define ERROR_JSON_CREATE -6                                         // JSON创建失败

#endif // SMART_HOME_CONFIG_H
