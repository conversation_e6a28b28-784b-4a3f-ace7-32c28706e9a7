#ifndef BUTTON_CONTROL_H
#define BUTTON_CONTROL_H

#include "smart_home_config.h"

/**
 * @file button_control.h
 * @brief 智能家居系统按键控制模块接口定义
 * @details 本模块提供GPIO按键控制功能，支持风扇、灯光和主电源控制
 *          使用libgpiod库进行GPIO操作
 * <AUTHOR> Home System Team
 * @version 1.0.0
 */

// GPIO配置
#define GPIO_CHIP_LABEL "GPIOF"
#define KEY1_PIN 9  // PF9 - 灯光控制按键
#define KEY2_PIN 7  // PF7 - 主电源控制按键  
#define KEY3_PIN 8  // PF8 - 风扇控制按键

// 按键状态枚举
typedef enum {
    BUTTON_RELEASED = 0,
    BUTTON_PRESSED = 1
} button_state_t;

// 风扇按键状态枚举
typedef enum {
    FAN_BUTTON_OFF = 0,
    FAN_BUTTON_ON = 1
} fan_button_state_t;

// 灯光模式枚举
typedef enum {
    LIGHT_MODE_ALL_OFF = 0,     // 全灭
    LIGHT_MODE_1_ONLY,          // 灯1亮
    LIGHT_MODE_2_ONLY,          // 灯2亮
    LIGHT_MODE_3_ONLY,          // 灯3亮
    LIGHT_MODE_4_ONLY,          // 灯4亮
    LIGHT_MODE_5_ONLY,          // 灯5亮
    LIGHT_MODE_MAX
} light_button_mode_t;

// 主电源状态枚举
typedef enum {
    MAIN_POWER_BUTTON_OFF = 0,
    MAIN_POWER_BUTTON_ON = 1
} main_power_button_state_t;

/**
 * @brief 初始化按键控制模块
 * @details 初始化GPIO芯片和所有按键引脚，设置为输入模式
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 初始化成功
 * @retval ERROR_FILE_OPEN GPIO芯片打开失败
 */
int button_control_init(void);

/**
 * @brief 检测风扇控制按键状态
 * @details 读取KEY3(PF8)引脚状态
 * @return 按键状态：1=按下，0=未按下
 */
int button_check_fan_key(void);

/**
 * @brief 检测灯光控制按键状态
 * @details 读取KEY1(PF9)引脚状态
 * @return 按键状态：1=按下，0=未按下
 */
int button_check_light_key(void);

/**
 * @brief 检测主电源控制按键状态
 * @details 读取KEY2(PF7)引脚状态
 * @return 按键状态：1=按下，0=未按下
 */
int button_check_main_power_key(void);

/**
 * @brief 风扇按键切换处理
 * @details 切换风扇开关状态，调用现有的风扇控制函数
 */
void button_handle_fan_toggle(void);

/**
 * @brief 灯光按键切换处理
 * @details 循环切换灯光模式：全灭->灯1->灯2->灯3->灯4->灯5->全灭
 */
void button_handle_light_toggle(void);

/**
 * @brief 主电源按键切换处理
 * @details 切换所有设备的总电源状态
 */
void button_handle_main_power_toggle(void);

/**
 * @brief 获取当前风扇按键状态
 * @return 风扇状态：FAN_BUTTON_ON=开启，FAN_BUTTON_OFF=关闭
 */
fan_button_state_t button_get_fan_state(void);

/**
 * @brief 获取当前灯光模式
 * @return 当前灯光模式枚举值
 */
light_button_mode_t button_get_light_mode(void);

/**
 * @brief 获取主电源状态
 * @return 主电源状态：MAIN_POWER_BUTTON_ON=开启，MAIN_POWER_BUTTON_OFF=关闭
 */
main_power_button_state_t button_get_main_power_state(void);

/**
 * @brief 按键扫描和处理函数
 * @details 扫描所有按键状态，处理按键事件（带防抖）
 *          应在主循环中定期调用
 */
void button_scan_and_handle(void);

/**
 * @brief 释放按键控制资源
 * @details 释放GPIO资源，关闭所有设备
 */
void button_control_cleanup(void);

#endif // BUTTON_CONTROL_H
